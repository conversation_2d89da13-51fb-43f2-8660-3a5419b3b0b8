["C:\\xampp-8.2-new\\htdocs\\examhots\\examhots_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\image-bg.jpg", "C:\\xampp-8.2-new\\htdocs\\examhots\\examhots_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\login-bg.jpg", "C:\\xampp-8.2-new\\htdocs\\examhots\\examhots_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\logo-hots.png", "C:\\xampp-8.2-new\\htdocs\\examhots\\examhots_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\logo.png", "C:\\xampp-8.2-new\\htdocs\\examhots\\examhots_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\no-data.gif", "C:\\xampp-8.2-new\\htdocs\\examhots\\examhots_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\profile.jpg", "C:\\xampp-8.2-new\\htdocs\\examhots\\examhots_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\success.gif", "C:\\xampp-8.2-new\\htdocs\\examhots\\examhots_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf", "C:\\xampp-8.2-new\\htdocs\\examhots\\examhots_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\fonts\\MaterialIcons-Regular.otf", "C:\\xampp-8.2-new\\htdocs\\examhots\\examhots_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\shaders\\ink_sparkle.frag", "C:\\xampp-8.2-new\\htdocs\\examhots\\examhots_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\AssetManifest.json", "C:\\xampp-8.2-new\\htdocs\\examhots\\examhots_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\AssetManifest.bin", "C:\\xampp-8.2-new\\htdocs\\examhots\\examhots_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\FontManifest.json", "C:\\xampp-8.2-new\\htdocs\\examhots\\examhots_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\NOTICES.Z", "C:\\xampp-8.2-new\\htdocs\\examhots\\examhots_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\NativeAssetsManifest.json", "C:\\xampp-8.2-new\\htdocs\\examhots\\examhots_app\\build\\app\\intermediates\\flutter\\release\\x86_64\\app.so", "C:\\xampp-8.2-new\\htdocs\\examhots\\examhots_app\\build\\app\\intermediates\\flutter\\release\\arm64-v8a\\app.so", "C:\\xampp-8.2-new\\htdocs\\examhots\\examhots_app\\build\\app\\intermediates\\flutter\\release\\armeabi-v7a\\app.so"]