<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ExamTemporaryAnswer extends Model
{
    protected $table = 'exam_temporary_answers';

    protected $fillable = [
        'examid',
        'studentid',
        'questionid',
        'attempt',
        'question_type',
        'selected_answer',
        'text_answer',
        'image_path',
        'image_paths', // New field for multiple images
        'has_attachment'
    ];

    protected $casts = [
        'has_attachment' => 'boolean',
        'image_paths' => 'array' // Cast JSON to array
    ];

    // Relationships
    public function exam()
    {
        return $this->belongsTo(Exam::class, 'examid');
    }

    public function student()
    {
        return $this->belongsTo(Student::class, 'studentid');
    }

    public function question()
    {
        return $this->belongsTo(Question::class, 'questionid');
    }
}
