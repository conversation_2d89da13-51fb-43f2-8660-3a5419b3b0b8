<variant
    name="release"
    package="io.flutter.plugins.camera"
    minSdkVersion="21"
    targetSdkVersion="21"
    mergedManifest="C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml"
    manifestMergeReport="C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android\intermediates\default_proguard_files\global\proguard-android.txt-8.7.0"
    partialResultsDir="C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android\intermediates\lint_partial_results\release\lintAnalyzeRelease\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android\intermediates\javac\release\compileReleaseJavaWithJavac\classes;C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android\intermediates\compile_r_class_jar\release\generateReleaseRFile\R.jar"
      type="MAIN"
      applicationId="io.flutter.plugins.camera"
      generatedSourceFolders="C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android\generated\ap_generated_sources\release\out;C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android\generated\source\buildConfig\release"
      generatedResourceFolders="C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\daea310d27237c4e52d6b3b83749b351\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
