<?php

use App\Http\Controllers\Api\AuthApiController;
use App\Http\Controllers\Api\QuestionMaterialApiController;
use App\Http\Controllers\Api\QuestionApiController;
use App\Http\Controllers\Api\TeacherApiController;
use App\Http\Controllers\Api\StudentClassApiController;
use App\Http\Controllers\Api\StudentApiController;
use App\Http\Controllers\Api\AdminApiController;
use App\Http\Controllers\Api\ExamApiController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Test route
Route::get('/test', function () {
    return response()->json(['message' => 'API is working']);
});

// Authentication routes
Route::prefix('auth')->group(function () {
    Route::post('/login', [AuthApiController::class, 'login']);
    Route::post('/logout', [AuthApiController::class, 'logout'])->middleware('auth:sanctum');
    Route::get('/profile', [AuthApiController::class, 'profile'])->middleware('auth:sanctum');
    Route::put('/profile', [AuthApiController::class, 'updateProfile'])->middleware('auth:sanctum');
    Route::put('/change-password', [AuthApiController::class, 'changePassword'])->middleware('auth:sanctum');
});

// Question Material routes (protected)
Route::middleware('auth:sanctum')->group(function () {
    Route::prefix('question-materials')->group(function () {
        Route::get('/', [QuestionMaterialApiController::class, 'index']);
        Route::get('/{id}', [QuestionMaterialApiController::class, 'show']);
        Route::post('/', [QuestionMaterialApiController::class, 'store']);
        Route::put('/{id}', [QuestionMaterialApiController::class, 'update']);
        Route::put('/{id}/scores', [QuestionMaterialApiController::class, 'updateScores']);
        Route::delete('/{id}', [QuestionMaterialApiController::class, 'destroy']);
    });

    // Question routes (protected)
    Route::prefix('questions')->group(function () {
        Route::post('/material/{materialId}', [QuestionApiController::class, 'store']);
        Route::put('/{id}', [QuestionApiController::class, 'update']);
        Route::post('/{id}/update', [QuestionApiController::class, 'update']); // Alternative for multipart
        Route::delete('/{id}', [QuestionApiController::class, 'destroy']);
        Route::post('/upload-image', [QuestionApiController::class, 'uploadImage']);
    });

    // Teacher management routes (admin only)
    Route::prefix('teachers')->middleware('admin')->group(function () {
        Route::get('/', [TeacherApiController::class, 'index']);
        Route::get('/{id}', [TeacherApiController::class, 'show']);
        Route::post('/', [TeacherApiController::class, 'store']);
        Route::put('/{id}', [TeacherApiController::class, 'update']);
        Route::delete('/{id}', [TeacherApiController::class, 'destroy']);
    });

    // Class management routes (admin only)
    Route::prefix('classes')->middleware('admin')->group(function () {
        Route::get('/', [StudentClassApiController::class, 'index']);
        Route::get('/teachers', [StudentClassApiController::class, 'getTeachers']);
        Route::get('/{id}', [StudentClassApiController::class, 'show']);
        Route::post('/', [StudentClassApiController::class, 'store']);
        Route::put('/{id}', [StudentClassApiController::class, 'update']);
        Route::delete('/{id}', [StudentClassApiController::class, 'destroy']);
    });

    // Student management routes (admin only)
    Route::prefix('students')->middleware('admin')->group(function () {
        Route::get('/', [StudentApiController::class, 'index']);
        Route::get('/classes', [StudentApiController::class, 'getClasses']);
        Route::get('/{id}', [StudentApiController::class, 'show']);
        Route::post('/', [StudentApiController::class, 'store']);
        Route::put('/{id}', [StudentApiController::class, 'update']);
        Route::delete('/{id}', [StudentApiController::class, 'destroy']);
    });

    // Admin management routes (admin only)
    Route::prefix('admins')->middleware('admin')->group(function () {
        Route::get('/', [AdminApiController::class, 'index']);
        Route::get('/{id}', [AdminApiController::class, 'show']);
        Route::post('/', [AdminApiController::class, 'store']);
        Route::put('/{id}', [AdminApiController::class, 'update']);
        Route::delete('/{id}', [AdminApiController::class, 'destroy']);

        // Admin exam management routes
        Route::prefix('exams')->group(function () {
            Route::get('/', [ExamApiController::class, 'adminIndex']);
            Route::get('/form-data', [ExamApiController::class, 'adminGetFormData']);
            Route::get('/{id}', [ExamApiController::class, 'adminShow']);
            Route::post('/', [ExamApiController::class, 'adminStore']);
            Route::put('/{id}', [ExamApiController::class, 'adminUpdate']);
            Route::delete('/{id}', [ExamApiController::class, 'adminDestroy']);
        });
    });

    // Shared exam routes (teacher and student access)
    Route::prefix('exams')->group(function () {
        Route::get('/questions/{materialId}', [ExamApiController::class, 'getQuestionsByMaterial']);
    });

    // Exam management routes (teacher access)
    Route::prefix('exams')->middleware('teacher')->group(function () {
        Route::get('/', [ExamApiController::class, 'index']);
        Route::get('/form-data', [ExamApiController::class, 'getFormData']);
        Route::get('/{id}', [ExamApiController::class, 'show']);
        Route::get('/{id}/participants', [ExamApiController::class, 'getExamParticipants']);
        Route::get('/{examId}/student/{studentId}/detail', [ExamApiController::class, 'getStudentExamDetail']);
        Route::post('/{examId}/student/{studentId}/essay-score', [ExamApiController::class, 'updateStudentEssayScore']);
        Route::post('/', [ExamApiController::class, 'store']);
        Route::put('/{id}', [ExamApiController::class, 'update']);
        Route::delete('/{id}', [ExamApiController::class, 'destroy']);

        // Export routes for Flutter app
        Route::get('/{id}/export/excel', [ExamApiController::class, 'exportExcel']);
        Route::get('/{id}/export/pdf', [ExamApiController::class, 'exportPdf']);
    });

    // Student exam routes (authenticated users)
    Route::prefix('exams')->group(function () {
        Route::get('/{id}/check-time', [ExamApiController::class, 'checkExamTime']);
        Route::post('/validate-token', [ExamApiController::class, 'validateToken']);
        Route::get('/{id}/questions', [ExamApiController::class, 'getExamQuestions']);
        Route::get('/{id}/result', [ExamApiController::class, 'getExamResult']);
        Route::get('/{id}/temporary-answers', [ExamApiController::class, 'getTemporaryAnswers']);
        Route::post('/save-temporary-answer', [ExamApiController::class, 'saveTemporaryAnswer']);
        Route::post('/upload-answer-image', [ExamApiController::class, 'uploadAnswerImage']);
        Route::post('/upload-answer-images', [ExamApiController::class, 'uploadAnswerImages']); // New endpoint for multiple images
        Route::post('/{id}/submit', [ExamApiController::class, 'submitAnswers']);
    });

    // Student exam routes
    Route::get('/student/exams', [ExamApiController::class, 'getStudentExams']);
});
