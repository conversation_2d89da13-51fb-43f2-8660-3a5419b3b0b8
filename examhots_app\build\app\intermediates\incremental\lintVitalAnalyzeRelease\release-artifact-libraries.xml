<libraries>
  <library
      name="__local_aars__:C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\app\intermediates\flutter\release\libs.jar:unspecified@jar"
      jars="C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\app\intermediates\flutter\release\libs.jar"
      resolved="__local_aars__:C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\app\intermediates\flutter\release\libs.jar:unspecified"/>
  <library
      name=":@@:file_picker::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\763e6c6e4972916d5e68fc4e613c5fab\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\763e6c6e4972916d5e68fc4e613c5fab\transformed\out\jars\libs\R.jar"
      resolved="com.mr.flutter.plugin.filepicker:file_picker:1.0-SNAPSHOT"
      partialResultsDir="C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\file_picker\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\763e6c6e4972916d5e68fc4e613c5fab\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:shared_preferences_android::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a43c8188b47c12866eed4eeea8f234e\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a43c8188b47c12866eed4eeea8f234e\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.sharedpreferences:shared_preferences_android:1.0-SNAPSHOT"
      partialResultsDir="C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\shared_preferences_android\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a43c8188b47c12866eed4eeea8f234e\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.8.22\4dabb8248310d833bb6a8b516024a91fd3d275c\kotlin-stdlib-jdk7-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22"/>
  <library
      name=":@@:camera_android::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97e46643133a0eef199540e2b4d53ba1\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97e46643133a0eef199540e2b4d53ba1\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.camera:camera_android:1.0-SNAPSHOT"
      partialResultsDir="C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97e46643133a0eef199540e2b4d53ba1\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:flutter_plugin_android_lifecycle::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe7a64445186619cf28fd3805abf35b\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe7a64445186619cf28fd3805abf35b\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.flutter_plugin_android_lifecycle:flutter_plugin_android_lifecycle:1.0"
      partialResultsDir="C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\flutter_plugin_android_lifecycle\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe7a64445186619cf28fd3805abf35b\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:image_picker_android::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c89d2857944c06f930f46b790a50461d\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c89d2857944c06f930f46b790a50461d\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.imagepicker:image_picker_android:1.0-SNAPSHOT"
      partialResultsDir="C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c89d2857944c06f930f46b790a50461d\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:path_provider_android::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bebfe18bfd42ae9c8ac1b37d15782ed3\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bebfe18bfd42ae9c8ac1b37d15782ed3\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.pathprovider:path_provider_android:1.0-SNAPSHOT"
      partialResultsDir="C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\path_provider_android\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bebfe18bfd42ae9c8ac1b37d15782ed3\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:permission_handler_android::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb42f397d0ab9f2d39508bd2e4de090a\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb42f397d0ab9f2d39508bd2e4de090a\transformed\out\jars\libs\R.jar"
      resolved="com.baseflow.permissionhandler:permission_handler_android:1.0"
      partialResultsDir="C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\permission_handler_android\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb42f397d0ab9f2d39508bd2e4de090a\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:url_launcher_android::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8e40c3d023fad5cd929e3dcdcf8c2f1\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8e40c3d023fad5cd929e3dcdcf8c2f1\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.urllauncher:url_launcher_android:1.0-SNAPSHOT"
      partialResultsDir="C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8e40c3d023fad5cd929e3dcdcf8c2f1\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.flutter:flutter_embedding_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.flutter\flutter_embedding_release\1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa\e49eb3292687e1b573c417f19ce5fb36ba995ecc\flutter_embedding_release-1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa.jar"
      resolved="io.flutter:flutter_embedding_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa"/>
  <library
      name="androidx.fragment:fragment:1.7.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\812b0debb255957f3fcc9a2669f27c22\transformed\fragment-1.7.1\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.7.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\812b0debb255957f3fcc9a2669f27c22\transformed\fragment-1.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.9.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e57d2fe08522bc82b0ac6f830a2f178d\transformed\jetified-activity-1.9.3\jars\classes.jar"
      resolved="androidx.activity:activity:1.9.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e57d2fe08522bc82b0ac6f830a2f178d\transformed\jetified-activity-1.9.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf593104563b66144c3b6dc7fc4802c1\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf593104563b66144c3b6dc7fc4802c1\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbaed96c1a439e6be117bba0ac489cd1\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbaed96c1a439e6be117bba0ac489cd1\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10174209784641d09178a7319da166d9\transformed\lifecycle-livedata-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10174209784641d09178a7319da166d9\transformed\lifecycle-livedata-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29dd382751d18a331db7f64e6c11e7d7\transformed\lifecycle-viewmodel-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29dd382751d18a331db7f64e6c11e7d7\transformed\lifecycle-viewmodel-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\249883554335c13e5e36e083666dc7eb\transformed\jetified-lifecycle-viewmodel-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-android:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\249883554335c13e5e36e083666dc7eb\transformed\jetified-lifecycle-viewmodel-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae1ff5a879a7079b2c1f954441e486a\transformed\lifecycle-livedata-core-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae1ff5a879a7079b2c1f954441e486a\transformed\lifecycle-livedata-core-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555a11e43f21cd1b0cbc43c2d4eef123\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555a11e43f21cd1b0cbc43c2d4eef123\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.15.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57b870091242cb9f06317b766a899002\transformed\jetified-core-ktx-1.15.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.15.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57b870091242cb9f06317b766a899002\transformed\jetified-core-ktx-1.15.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71467fe7caeb25f99b2fabbeba6edee\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71467fe7caeb25f99b2fabbeba6edee\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\348e9995eb75a687bf31011ee5dfb249\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\348e9995eb75a687bf31011ee5dfb249\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.15.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43a41165a90de7b0bfc02b6ed57eebd0\transformed\core-1.15.0\jars\classes.jar"
      resolved="androidx.core:core:1.15.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43a41165a90de7b0bfc02b6ed57eebd0\transformed\core-1.15.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82f9f9e2255face548965e2c210871f5\transformed\jetified-lifecycle-runtime-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-android:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82f9f9e2255face548965e2c210871f5\transformed\jetified-lifecycle-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-jvm\2.8.7\7174a594afb73a9ad9ac9074ce78b94af3cc52a7\lifecycle-common-jvm-2.8.7.jar"
      resolved="androidx.lifecycle:lifecycle-common-jvm:2.8.7"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.8.7@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.8.7\2ad14aed781c4a73ed4dbb421966d408a0a06686\lifecycle-common-java8-2.8.7.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.8.7"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43ebce9c1ba25a34b2589ddcb28a686a\transformed\jetified-lifecycle-process-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43ebce9c1ba25a34b2589ddcb28a686a\transformed\jetified-lifecycle-process-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window:window:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\jars\classes.jar"
      resolved="androidx.window:window:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window:window-java:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a5284c6eeabc2d8b6190c9576956065\transformed\jetified-window-java-1.2.0\jars\classes.jar"
      resolved="androidx.window:window-java:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a5284c6eeabc2d8b6190c9576956065\transformed\jetified-window-java-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71bc0c4f568ebbce86450b445dadf8a3\transformed\jetified-annotation-experimental-1.4.1\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71bc0c4f568ebbce86450b445dadf8a3\transformed\jetified-annotation-experimental-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae5f57bd372fc0b34c3bf33092f8edd1\transformed\jetified-savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae5f57bd372fc0b34c3bf33092f8edd1\transformed\jetified-savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae03ddb689964a197cc9572a48c09923\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae03ddb689964a197cc9572a48c09923\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24b2d70c983456e4678be6d7afa07780\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24b2d70c983456e4678be6d7afa07780\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.collection:collection-jvm:1.4.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.4.2\bc60b5568a66d765a9fe8e266fd0c6c727e0b50b\collection-jvm-1.4.2.jar"
      resolved="androidx.collection:collection-jvm:1.4.2"/>
  <library
      name="androidx.annotation:annotation-jvm:1.9.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.9.1\b17951747e38bf3986a24431b9ba0d039958aa5f\annotation-jvm-1.9.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.9.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.24\9928532f12c66ad816a625b3f9984f8368ca6d2b\kotlin-stdlib-1.9.24.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.9.24"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.8.22\b25c86d47d6b962b9cf0f8c3f320c8a10eea3dd1\kotlin-stdlib-jdk8-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22"/>
  <library
      name="io.flutter:armeabi_v7a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.flutter\armeabi_v7a_release\1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa\4a1622e4221ab8ec37d49ea9525f6bac5bffb95e\armeabi_v7a_release-1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa.jar"
      resolved="io.flutter:armeabi_v7a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa"/>
  <library
      name="io.flutter:arm64_v8a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.flutter\arm64_v8a_release\1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa\93179db9fcde85c97d8080f6eba90a3a219e1d7\arm64_v8a_release-1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa.jar"
      resolved="io.flutter:arm64_v8a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa"/>
  <library
      name="io.flutter:x86_64_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.flutter\x86_64_release\1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa\92310057aa1fe51d117a6f550f682f1994f14d8d\x86_64_release-1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa.jar"
      resolved="io.flutter:x86_64_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77d6589d54f70503af1ad7bd5c95eb07\transformed\jetified-startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77d6589d54f70503af1ad7bd5c95eb07\transformed\jetified-startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67e6cf8dd6496a1e1ab42bfbc69e3371\transformed\jetified-tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67e6cf8dd6496a1e1ab42bfbc69e3371\transformed\jetified-tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a9ec07ac357bc15a8d85f12e6d4189c\transformed\jetified-relinker-1.4.5\jars\classes.jar"
      resolved="com.getkeepsafe.relinker:relinker:1.4.5"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a9ec07ac357bc15a8d85f12e6d4189c\transformed\jetified-relinker-1.4.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.preference:preference:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0aea3dee6044b352a54f8fb9c4a12c07\transformed\preference-1.2.1\jars\classes.jar"
      resolved="androidx.preference:preference:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0aea3dee6044b352a54f8fb9c4a12c07\transformed\preference-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32d7d149835c8c00f0697dfcab9adb84\transformed\appcompat-1.1.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32d7d149835c8c00f0697dfcab9adb84\transformed\appcompat-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.7.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd8f2b9cf49e7284b6695565297622e3\transformed\jetified-fragment-ktx-1.7.1\jars\classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.7.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd8f2b9cf49e7284b6695565297622e3\transformed\jetified-fragment-ktx-1.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.9.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d0eff89ceab5cf34de5203e536a9f16\transformed\jetified-activity-ktx-1.9.3\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.9.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d0eff89ceab5cf34de5203e536a9f16\transformed\jetified-activity-ktx-1.9.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2df645d79e711076ea45b6a8385e99c\transformed\recyclerview-1.0.0\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2df645d79e711076ea45b6a8385e99c\transformed\recyclerview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6fb5bbbc1627b43bb066b846f50b0b9c\transformed\legacy-support-core-ui-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-ui:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6fb5bbbc1627b43bb066b846f50b0b9c\transformed\legacy-support-core-ui-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c4a6ad3f7b10ac103c0346368799501\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c4a6ad3f7b10ac103c0346368799501\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f3b092f04c0253e979c83421f120f88\transformed\jetified-savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f3b092f04c0253e979c83421f120f88\transformed\jetified-savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d499387da88aabdcb63c1ff12f420649\transformed\jetified-lifecycle-runtime-ktx-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d499387da88aabdcb63c1ff12f420649\transformed\jetified-lifecycle-runtime-ktx-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6fcc9965ea56671b4ef138d028bfa28a\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6fcc9965ea56671b4ef138d028bfa28a\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5683f3a75f4aae74864d56d25d09ab49\transformed\browser-1.8.0\jars\classes.jar"
      resolved="androidx.browser:browser:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5683f3a75f4aae74864d56d25d09ab49\transformed\browser-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f3ef3caec9d424994a2628e26767d1d4\transformed\slidingpanelayout-1.2.0\jars\classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f3ef3caec9d424994a2628e26767d1d4\transformed\slidingpanelayout-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ff8cdab1140e9ed76ee2cc1c84e5b27\transformed\jetified-appcompat-resources-1.1.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ff8cdab1140e9ed76ee2cc1c84e5b27\transformed\jetified-appcompat-resources-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9108df622b0b8e8dbba78b566c3bdc67\transformed\drawerlayout-1.0.0\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9108df622b0b8e8dbba78b566c3bdc67\transformed\drawerlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b6435e177046c519b4e9a09d53555b3f\transformed\coordinatorlayout-1.0.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b6435e177046c519b4e9a09d53555b3f\transformed\coordinatorlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e9c3e49153aa47f3a49791ab647f926\transformed\transition-1.4.1\jars\classes.jar"
      resolved="androidx.transition:transition:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e9c3e49153aa47f3a49791ab647f926\transformed\transition-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0fa1677c034ef049b8c12b4202dcc8c4\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0fa1677c034ef049b8c12b4202dcc8c4\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2111a692760d2213c419f21507346c83\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2111a692760d2213c419f21507346c83\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbe59546d7834ae0953d7d1775d43fac\transformed\swiperefreshlayout-1.0.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbe59546d7834ae0953d7d1775d43fac\transformed\swiperefreshlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7295a6e9f96a0daeed321ae2faac1f7b\transformed\asynclayoutinflater-1.0.0\jars\classes.jar"
      resolved="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7295a6e9f96a0daeed321ae2faac1f7b\transformed\asynclayoutinflater-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\747df56e3edd1e12435e5eae9046f54c\transformed\exifinterface-1.3.7\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\747df56e3edd1e12435e5eae9046f54c\transformed\exifinterface-1.3.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7620a15c947fbbae2a35989fac4ced29\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7620a15c947fbbae2a35989fac4ced29\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-preferences-external-protobuf:1.1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-external-protobuf\1.1.3\ddd0a2d64e3c928359c993d1291e535d5d7fc9a3\datastore-preferences-external-protobuf-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-external-protobuf:1.1.3"/>
  <library
      name="androidx.datastore:datastore-preferences-proto:1.1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-proto\1.1.3\6d7430ed8d2b5f2b8675dad8d196ba5dd710921b\datastore-preferences-proto-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-proto:1.1.3"/>
  <library
      name="androidx.datastore:datastore-preferences-core-jvm:1.1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-core-jvm\1.1.3\fb991f11389ccf2a5d5d4c99783ff958bc400\datastore-preferences-core-jvm-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-core-jvm:1.1.3"/>
  <library
      name="androidx.datastore:datastore-core-okio-jvm:1.1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-core-okio-jvm\1.1.3\ce08f132812044a9778547b299fd812e34dbd602\datastore-core-okio-jvm-1.1.3.jar"
      resolved="androidx.datastore:datastore-core-okio-jvm:1.1.3"/>
  <library
      name="androidx.datastore:datastore-core-android:1.1.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\decb1acca50446f7d04f9c3baaa30a43\transformed\jetified-datastore-core-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-core-android:1.1.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\decb1acca50446f7d04f9c3baaa30a43\transformed\jetified-datastore-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-preferences-android:1.1.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4ce3ca3f2edcf65c5795f97027f83c4\transformed\jetified-datastore-preferences-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-preferences-android:1.1.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4ce3ca3f2edcf65c5795f97027f83c4\transformed\jetified-datastore-preferences-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-android:1.1.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b71b52b2a70c6b10140d87dce8249e38\transformed\jetified-datastore-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-android:1.1.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b71b52b2a70c6b10140d87dce8249e38\transformed\jetified-datastore-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.4.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.4.2\2ad14aed781c4a73ed4dbb421966d408a0a06686\collection-ktx-1.4.2.jar"
      resolved="androidx.collection:collection-ktx:1.4.2"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cdf4cc3b708776cdc2805e90540cbeb3\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cdf4cc3b708776cdc2805e90540cbeb3\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window.extensions.core:core:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55a192cd1e8c493370fa6465d68c1145\transformed\jetified-core-1.0.0\jars\classes.jar"
      resolved="androidx.window.extensions.core:core:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55a192cd1e8c493370fa6465d68c1145\transformed\jetified-core-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\38895ff92e9ea1cab9c97b52c490e983\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\38895ff92e9ea1cab9c97b52c490e983\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14a7b710af8666cefae7796bd89d044c\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14a7b710af8666cefae7796bd89d044c\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adc76dc2b16c544bb53f946048eea1be\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adc76dc2b16c544bb53f946048eea1be\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-parcelize-runtime\1.9.22\de4a21d6560cadd035c69ba3af3ad1afecc95299\kotlin-parcelize-runtime-1.9.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-android-extensions-runtime\1.9.22\ee3bc0c3b55cb516ac92d6a093e1b939166b86a2\kotlin-android-extensions-runtime-1.9.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22"/>
  <library
      name="com.squareup.okio:okio-jvm:3.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.4.0\4e8bd78a52ab935ce383d0092646922154295e54\okio-jvm-3.4.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.4.0"/>
  <library
      name="org.apache.tika:tika-core:3.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.tika\tika-core\3.2.0\9232bb3c71f231e8228f570071c0e1ea29d40115\tika-core-3.2.0.jar"
      resolved="org.apache.tika:tika-core:3.2.0"/>
  <library
      name="org.slf4j:slf4j-api:2.0.17@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.slf4j\slf4j-api\2.0.17\d9e58ac9c7779ba3bf8142aff6c830617a7fe60f\slf4j-api-2.0.17.jar"
      resolved="org.slf4j:slf4j-api:2.0.17"/>
  <library
      name="commons-io:commons-io:2.19.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-io\commons-io\2.19.0\1f8d4a99ba72b77aa69101175efc79b0c7dcdd7e\commons-io-2.19.0.jar"
      resolved="commons-io:commons-io:2.19.0"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\1.0\c949a840a6acbc5268d088e47b04177bf90b3cad\listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
</libraries>
