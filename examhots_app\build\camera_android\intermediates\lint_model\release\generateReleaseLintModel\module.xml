<lint-module
    format="1"
    dir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\camera_android-0.10.10+3\android"
    name=":camera_android"
    type="LIBRARY"
    maven="io.flutter.plugins.camera:camera_android:1.0-SNAPSHOT"
    agpVersion="8.7.0"
    buildFolder="C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\sdk\platforms\android-35\android.jar;C:\Users\<USER>\AppData\Local\Android\sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-35"
    neverShrinking="true">
  <lintOptions
      disable="AndroidGradlePluginVersion,InvalidPackage,GradleDependency,NewerVersionAvailable"
      abortOnError="true"
      absolutePaths="true"
      checkAllWarnings="true"
      warningsAsErrors="true"
      checkReleaseBuilds="true"
      explainIssues="true">
    <severities>
      <severity
        id="AndroidGradlePluginVersion"
        severity="IGNORE" />
      <severity
        id="GradleDependency"
        severity="IGNORE" />
      <severity
        id="InvalidPackage"
        severity="IGNORE" />
      <severity
        id="NewerVersionAvailable"
        severity="IGNORE" />
    </severities>
  </lintOptions>
  <variant name="release"/>
</lint-module>
