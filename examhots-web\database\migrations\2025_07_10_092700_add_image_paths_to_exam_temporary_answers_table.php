<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('exam_temporary_answers', function (Blueprint $table) {
            $table->text('image_paths')->nullable()->after('image_path'); // JSON array for multiple images
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('exam_temporary_answers', function (Blueprint $table) {
            $table->dropColumn('image_paths');
        });
    }
};
