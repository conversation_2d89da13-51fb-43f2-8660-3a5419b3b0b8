<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exam_temporary_answers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('examid');
            $table->unsignedBigInteger('studentid');
            $table->unsignedBigInteger('questionid');
            $table->integer('attempt')->default(1);
            $table->string('question_type');
            $table->string('selected_answer')->nullable();
            $table->text('text_answer')->nullable();
            $table->string('image_path')->nullable(); // Keep for backward compatibility
            $table->text('image_paths')->nullable(); // New field for multiple images (JSON array)
            $table->boolean('has_attachment')->default(false);
            $table->timestamps();

            $table->foreign('examid')->references('id')->on('exam')->onDelete('cascade');
            $table->foreign('studentid')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('questionid')->references('id')->on('question')->onDelete('cascade');

            // Ensure one answer per question per attempt
            $table->unique(['examid', 'studentid', 'questionid', 'attempt'], 'temp_answers_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exam_temporary_answers');
    }
};
