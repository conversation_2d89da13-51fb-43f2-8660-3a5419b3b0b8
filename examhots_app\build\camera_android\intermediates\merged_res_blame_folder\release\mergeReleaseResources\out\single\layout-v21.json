[{"merged": "io.flutter.plugins.camera.camera_android-release-25:/layout-v21/notification_template_custom_big.xml", "source": "io.flutter.plugins.camera.camera_android-core-1.13.1-17:/layout-v21/notification_template_custom_big.xml"}, {"merged": "io.flutter.plugins.camera.camera_android-release-25:/layout-v21/notification_action_tombstone.xml", "source": "io.flutter.plugins.camera.camera_android-core-1.13.1-17:/layout-v21/notification_action_tombstone.xml"}, {"merged": "io.flutter.plugins.camera.camera_android-release-25:/layout-v21/notification_action.xml", "source": "io.flutter.plugins.camera.camera_android-core-1.13.1-17:/layout-v21/notification_action.xml"}, {"merged": "io.flutter.plugins.camera.camera_android-release-25:/layout-v21/notification_template_icon_group.xml", "source": "io.flutter.plugins.camera.camera_android-core-1.13.1-17:/layout-v21/notification_template_icon_group.xml"}]