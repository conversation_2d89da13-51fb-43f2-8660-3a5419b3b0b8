class ExamResult {
  final int score;
  final DateTime submittedAt;
  final String status;
  final int attempt;
  final int totalAttempts;
  final bool hasEssayQuestions;

  ExamResult({
    required this.score,
    required this.submittedAt,
    required this.status,
    required this.attempt,
    required this.totalAttempts,
    this.hasEssayQuestions = false,
  });

  // Helper method to safely parse dynamic values to int
  static int _parseToInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value) ?? 0;
    return 0;
  }

  factory ExamResult.fromJson(Map<String, dynamic> json) {
    return ExamResult(
      score: _parseToInt(json['score']),
      submittedAt: DateTime.parse(json['submitted_at']),
      status: json['status'] ?? 'completed',
      attempt:
          _parseToInt(json['attempt']) == 0 ? 1 : _parseToInt(json['attempt']),
      totalAttempts:
          _parseToInt(json['total_attempts']) == 0
              ? 1
              : _parseToInt(json['total_attempts']),
      hasEssayQuestions: json['has_essay_questions'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'score': score,
      'submitted_at': submittedAt.toIso8601String(),
      'status': status,
      'attempt': attempt,
      'total_attempts': totalAttempts,
      'has_essay_questions': hasEssayQuestions,
    };
  }

  // Helper method to get status display text
  String get statusDisplay {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'Belum Dikoreksi';
      case 'graded':
        return 'Selesai';
      case 'in_progress':
        return 'Sedang Berlangsung';
      default:
        return status;
    }
  }

  // Helper method to get formatted score
  String get formattedScore {
    return score.toString();
  }

  // Helper method to get formatted submitted date
  String get formattedSubmittedAt {
    return '${submittedAt.day}/${submittedAt.month}/${submittedAt.year} ${submittedAt.hour.toString().padLeft(2, '0')}:${submittedAt.minute.toString().padLeft(2, '0')}';
  }
}
