1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.examhots_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!-- Permissions for camera and file access -->
11    <uses-permission android:name="android.permission.CAMERA" />
11-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:3:5-65
11-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:3:22-62
12    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
12-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:4:5-80
12-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:4:22-77
13    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
13-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:5:5-81
13-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:5:22-78
14    <uses-permission android:name="android.permission.INTERNET" />
14-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:6:5-67
14-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:6:22-64
15    <!--
16         Required to query activities that can process text, see:
17         https://developer.android.com/training/package-visibility and
18         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
19
20         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
21    -->
22    <queries>
22-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:44:5-49:15
23        <intent>
23-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:45:9-48:18
24            <action android:name="android.intent.action.PROCESS_TEXT" />
24-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:46:13-73
24-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:46:21-70
25
26            <data android:mimeType="text/plain" />
26-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:47:13-51
26-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:47:19-48
27        </intent>
28        <intent>
28-->[:file_picker] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:18
29            <action android:name="android.intent.action.GET_CONTENT" />
29-->[:file_picker] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-72
29-->[:file_picker] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:21-69
30
31            <data android:mimeType="*/*" />
31-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:47:13-51
31-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:47:19-48
32        </intent>
33    </queries>
34
35    <uses-permission android:name="android.permission.RECORD_AUDIO" />
35-->[:camera_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-71
35-->[:camera_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-68
36
37    <permission
37-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43a41165a90de7b0bfc02b6ed57eebd0\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
38        android:name="com.example.examhots_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
38-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43a41165a90de7b0bfc02b6ed57eebd0\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
39        android:protectionLevel="signature" />
39-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43a41165a90de7b0bfc02b6ed57eebd0\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
40
41    <uses-permission android:name="com.example.examhots_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
41-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43a41165a90de7b0bfc02b6ed57eebd0\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
41-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43a41165a90de7b0bfc02b6ed57eebd0\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
42
43    <application
44        android:name="android.app.Application"
44-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:9:9-42
45        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
45-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43a41165a90de7b0bfc02b6ed57eebd0\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
46        android:extractNativeLibs="true"
47        android:icon="@mipmap/launcher_icon"
47-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:10:9-45
48        android:label="Hots UM" >
48-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:11:9-32
49        <activity
49-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:12:9-32:20
50            android:name="com.example.examhots_app.MainActivity"
50-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:13:13-41
51            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
51-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:14:13-163
52            android:exported="true"
52-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:15:13-36
53            android:hardwareAccelerated="true"
53-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:16:13-47
54            android:launchMode="singleTop"
54-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:17:13-43
55            android:taskAffinity=""
55-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:18:13-36
56            android:theme="@style/LaunchTheme"
56-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:19:13-47
57            android:windowSoftInputMode="adjustResize" >
57-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:20:13-55
58
59            <!--
60                 Specifies an Android theme to apply to this Activity as soon as
61                 the Android process has started. This theme is visible to the user
62                 while the Flutter UI initializes. After that, this theme continues
63                 to determine the Window background behind the Flutter UI.
64            -->
65            <meta-data
65-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:25:13-27:57
66                android:name="io.flutter.embedding.android.NormalTheme"
66-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:26:17-72
67                android:resource="@style/NormalTheme" />
67-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:27:17-54
68
69            <intent-filter>
69-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:28:13-31:29
70                <action android:name="android.intent.action.MAIN" />
70-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:29:17-69
70-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:29:25-66
71
72                <category android:name="android.intent.category.LAUNCHER" />
72-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:30:17-77
72-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:30:27-74
73            </intent-filter>
74        </activity>
75        <!--
76             Don't delete the meta-data below.
77             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
78        -->
79        <meta-data
79-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:35:9-37:33
80            android:name="flutterEmbedding"
80-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:36:13-44
81            android:value="2" />
81-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:37:13-30
82
83        <provider
83-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-17:20
84            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
84-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-82
85            android:authorities="com.example.examhots_app.flutter.image_provider"
85-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-74
86            android:exported="false"
86-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-37
87            android:grantUriPermissions="true" >
87-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-47
88            <meta-data
88-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:75
89                android:name="android.support.FILE_PROVIDER_PATHS"
89-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-67
90                android:resource="@xml/flutter_image_picker_file_paths" />
90-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:17-72
91        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
92        <service
92-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:19
93            android:name="com.google.android.gms.metadata.ModuleDependencies"
93-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-78
94            android:enabled="false"
94-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-36
95            android:exported="false" >
95-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
96            <intent-filter>
96-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-26:29
97                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
97-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-94
97-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-91
98            </intent-filter>
99
100            <meta-data
100-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-30:36
101                android:name="photopicker_activity:0:required"
101-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-63
102                android:value="" />
102-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-33
103        </service>
104
105        <activity
105-->[:url_launcher_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-11:74
106            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
106-->[:url_launcher_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-74
107            android:exported="false"
107-->[:url_launcher_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-37
108            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
108-->[:url_launcher_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-71
109
110        <provider
110-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43ebce9c1ba25a34b2589ddcb28a686a\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
111            android:name="androidx.startup.InitializationProvider"
111-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43ebce9c1ba25a34b2589ddcb28a686a\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:25:13-67
112            android:authorities="com.example.examhots_app.androidx-startup"
112-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43ebce9c1ba25a34b2589ddcb28a686a\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:26:13-68
113            android:exported="false" >
113-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43ebce9c1ba25a34b2589ddcb28a686a\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:27:13-37
114            <meta-data
114-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43ebce9c1ba25a34b2589ddcb28a686a\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
115                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
115-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43ebce9c1ba25a34b2589ddcb28a686a\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
116                android:value="androidx.startup" />
116-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43ebce9c1ba25a34b2589ddcb28a686a\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
117            <meta-data
117-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
118                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
119                android:value="androidx.startup" />
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
120        </provider>
121
122        <uses-library
122-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
123            android:name="androidx.window.extensions"
123-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
124            android:required="false" />
124-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
125        <uses-library
125-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
126            android:name="androidx.window.sidecar"
126-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
127            android:required="false" />
127-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
128
129        <receiver
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
130            android:name="androidx.profileinstaller.ProfileInstallReceiver"
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
131            android:directBootAware="false"
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
132            android:enabled="true"
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
133            android:exported="true"
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
134            android:permission="android.permission.DUMP" >
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
135            <intent-filter>
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
136                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
137            </intent-filter>
138            <intent-filter>
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
139                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
140            </intent-filter>
141            <intent-filter>
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
142                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
143            </intent-filter>
144            <intent-filter>
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
145                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
146            </intent-filter>
147        </receiver>
148    </application>
149
150</manifest>
