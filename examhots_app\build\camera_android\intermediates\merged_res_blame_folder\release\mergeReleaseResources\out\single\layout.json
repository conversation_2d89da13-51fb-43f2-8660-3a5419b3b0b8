[{"merged": "io.flutter.plugins.camera.camera_android-release-25:/layout/custom_dialog.xml", "source": "io.flutter.plugins.camera.camera_android-core-1.13.1-17:/layout/custom_dialog.xml"}, {"merged": "io.flutter.plugins.camera.camera_android-release-25:/layout/notification_template_part_chronometer.xml", "source": "io.flutter.plugins.camera.camera_android-core-1.13.1-17:/layout/notification_template_part_chronometer.xml"}, {"merged": "io.flutter.plugins.camera.camera_android-release-25:/layout/notification_template_part_time.xml", "source": "io.flutter.plugins.camera.camera_android-core-1.13.1-17:/layout/notification_template_part_time.xml"}, {"merged": "io.flutter.plugins.camera.camera_android-release-25:/layout/ime_secondary_split_test_activity.xml", "source": "io.flutter.plugins.camera.camera_android-core-1.13.1-17:/layout/ime_secondary_split_test_activity.xml"}, {"merged": "io.flutter.plugins.camera.camera_android-release-25:/layout/ime_base_split_test_activity.xml", "source": "io.flutter.plugins.camera.camera_android-core-1.13.1-17:/layout/ime_base_split_test_activity.xml"}]